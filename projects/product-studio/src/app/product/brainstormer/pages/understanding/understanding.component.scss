
.column-container {
  display: flex;
  // width: 100%;
  height: 100%;

  &.first-column,
  &.third-column {
    flex-direction: column; 
    gap: 0.5rem;
    // height: 100%;
    > awe-card {
      flex: 1;
    }
  }
  &.second-column {
    flex-direction: row; 
    gap: 0.5rem;
    > awe-card {
      flex: 1; 
    }
  }
}

.card-icon { 
  height: 40px;
  width: 40px;
  border-radius: 50%;

  .card-icon-img { 
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin: 0; 
  }
}
.card-actions { 
  display: flex;
  justify-content: flex-end;
  align-items: center;
  

  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0; 
    min-width: 0; 

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

:host ::ng-deep .awe-card-body ul li { 
  line-height: 1.4;
  word-wrap: break-word;
  overflow:hidden;
  
}

@media (min-width: 990px) and (max-width: 1440px) {
  .column-container {
    &.first-column,
    &.third-column {
     height: 100%
    }
  }

  .column-container.second-column {
    flex-direction: column; // Stack cards vertically
    gap: 0.5rem;
    height: 100%;

    > awe-card {
      width: 100%; 
      max-height: 32%;
      flex: none; 
      margin-bottom: 0.5rem; 

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Medium screens (md) - 768px to 991.98px
@media (max-width: 991.98px) and (min-width: 768px) {
  .canvas-row {
    flex-direction: row; 
  }

  .column-container {
    &.first-column,
    &.third-column {
      flex-direction: row; 
      gap: 0.5rem;
    }

    &.second-column { // Cards stack vertically
      flex-direction: column;
      max-height: 32%;
      gap: 0; // Use margin on cards instead of gap for vertical stacking

      > awe-card {
        width: 100%;
        min-height: 150px;
        margin-bottom: 0.5rem;
        flex-grow: 0; // Don't grow to fill vertical space
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // Adjust font size in projected header for MD screens
  .custom-card-header h6 {
    font-size: 0.8rem;
  }

}

// Small screens (sm) - max-width 767.98px
@media (max-width: 767.98px) {

  .column-container {
    flex-direction: column; // Stack all cards vertically
    gap: 0.5rem; 
    height: 100%;
    &.first-column,
    &.second-column,
    &.third-column {
      flex-direction: column;
      gap: 0; // Use margin on cards

      > awe-card {
        width: 100%;
        max-height: 250px;
        margin-bottom: 0.5rem;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Extra small screens (xs) - max-width 575.98px (Bootstrap 5's sm breakpoint is 576px)
// If using Bootstrap 4, xs is <576px.
@media (max-width: 575.98px) {
  .column-container > awe-card { // Target all awe-cards in column-containers
    min-height: 100px; // Further reduce min-height for very small screens
  }

  // Adjust styles for projected content on XS screens
  .custom-card-header h6 {
    font-size: 0.7rem;
  }
  .card-icon { // Projected icon
    width: 32px;
    height: 32px;

    .card-icon-img {
      width: 20px;
      height: 20px;
    }
  }
}