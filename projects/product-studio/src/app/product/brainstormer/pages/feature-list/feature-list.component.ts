import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms'; // Add FormsModule for ngModel
import { CdkDragDrop, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { HeadingComponent } from '@awe/play-comp-library'; // Your awe-heading
import { EditDialogComponent, DialogConfig, DropdownItem } from '../../components/edit-dialog/edit-dialog.component';
import { AweCardComponent } from '../../components/awe-card/awe-card.component'; // <--- IMPORT AWE CARD

interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}

interface FeatureSection {
  id: string;
  title: string; // 'Mo', 'S', 'Co', 'W'
  subtitle: string; // 'MUST HAVE', 'SHOULD HAVE', etc.
  features: FeatureCard[];
}

@Component({
  selector: 'app-feature-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule, // Add FormsModule for ngModel
    DragDropModule,
    HeadingComponent,
    EditDialogComponent,
    AweCardComponent // <--- ADD AWE CARD TO IMPORTS
  ],
  templateUrl: './feature-list.component.html',
  styleUrls: ['./feature-list.component.scss'] // Corrected property name
})
export class FeatureListComponent implements OnInit {
  // ... (rest of your existing TS code is fine) ...
  roboBallIcon: string = '/icons/robo_ball.svg'; // Not used in current HTML, but keep if needed later
  threeDotsIcon: string = 'icons/three-dot.svg';

  isDialogVisible: boolean = false;
  dialogConfig: DialogConfig = {
    showHeader: true,
    showFooter: true,
    showCloseButton: true,
    title: 'Edit Feature List',
    width: '600px',
    backdrop: true
  };

  dropdownItems: DropdownItem[] = [
    { label: 'Edit', action: 'edit', icon: 'fas fa-edit' },
    { label: 'Delete', action: 'delete', icon: 'fas fa-trash' }
  ];

  currentEditingFeature: FeatureCard | null = null;
  openDropdownId: string | null = null;

  sections: FeatureSection[] = [
    {
      id: 'must-have',
      title: 'Mo',
      subtitle: 'MUST HAVE',
      features: [
        {
          id: 'must-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card Allows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric cardAllows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'must-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'should-have',
      title: 'S',
      subtitle: 'SHOULD HAVE',
      features: [
        {
          id: 'should-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'should-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'could-have',
      title: 'Co',
      subtitle: 'COULD HAVE',
      features: [
        {
          id: 'could-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    },
    {
      id: 'wont-have',
      title: 'W',
      subtitle: 'WON\'T HAVE',
      features: [
        {
          id: 'wont-1',
          title: 'Contactless Payment Capability',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        },
        {
          id: 'wont-2',
          title: 'Secure Payment Processing',
          description: 'Allows user to make contactless payments using the biometric card',
          tags: ['Convenience', 'User experience', 'Technology']
        }
      ]
    }
  ];

  constructor() {}

  ngOnInit(): void {
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.dropdown-menu-container')) { // Check for a common parent for dropdown button & menu
        this.closeAllDropdowns();
      }
    });
  }


  getSectionIds(): string[] {
    return this.sections.map(section => section.id);
  }

  onDrop(event: CdkDragDrop<FeatureCard[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }

  addNewFeature(sectionId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      const newId = `${sectionId}-feature-${Date.now()}`; // More unique ID
      section.features.push({
        id: newId,
        title: 'New Feature Title',
        description: 'Describe the new feature here.',
        tags: ['New Tag']
      });
    }
  }

  deleteFeature(sectionId: string, featureId: string): void {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      section.features = section.features.filter(f => f.id !== featureId);
    }
    this.closeAllDropdowns(); // Ensure dropdown closes after deletion
  }

  toggleDropdown(featureId: string, event: Event): void {
    event.stopPropagation();
    this.openDropdownId = this.openDropdownId === featureId ? null : featureId;
  }

  isDropdownOpen(featureId: string): boolean {
    return this.openDropdownId === featureId;
  }

  closeAllDropdowns(): void {
    this.openDropdownId = null;
  }

  openEditDialog(feature: FeatureCard): void {
    this.currentEditingFeature = { ...feature }; // Edit a copy
    this.dialogConfig.title = `Edit: ${feature.title}`;
    this.isDialogVisible = true;
    this.closeAllDropdowns();
  }

  closeDialog(): void {
    this.isDialogVisible = false;
    this.currentEditingFeature = null;
  }

  onDialogDropdownAction(action: string): void {
    if (!this.currentEditingFeature) return;
    // This method seems tied to a dropdown *inside* the dialog,
    // based on your dialog component's (dropdownAction) output.
    // The card's dropdown actions (edit/delete) are handled directly.
    console.log('Dialog dropdown action:', action, 'for feature:', this.currentEditingFeature.title);
  }

  // This method is for direct delete from card, called by (click) on delete button
  handleDeleteFeatureFromCard(sectionId: string, featureId: string) {
    this.deleteFeature(sectionId, featureId);
    this.closeAllDropdowns();
  }

  // Update feature tags from input field
  updateFeatureTags(event: Event): void {
    if (!this.currentEditingFeature) return;

    const target = event.target as HTMLInputElement;
    const tagsString = target.value;
    this.currentEditingFeature.tags = tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag !== '');
  }

  // Save the edited feature back to the original data
  saveEditedFeature(): void {
    if (!this.currentEditingFeature) return;

    // Find the section and feature to update
    for (const section of this.sections) {
      const featureIndex = section.features.findIndex(f => f.id === this.currentEditingFeature!.id);
      if (featureIndex !== -1) {
        // Update the feature with the edited data
        section.features[featureIndex] = { ...this.currentEditingFeature };
        break;
      }
    }

    // Close the dialog
    this.closeDialog();
  }
}