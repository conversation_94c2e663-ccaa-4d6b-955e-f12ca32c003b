
.column-container {
  display: flex;
  width: 100%;
  height: 100%;

  &.first-column,
  &.third-column {
    flex-direction: column;
    gap: 0.5rem;
    > awe-card {
      flex: 1;
    }
  }
  &.second-column {
    flex-direction: row;
    gap: 0.5rem;
    > awe-card {
      flex: 1;
    }
  }
}

.card-icon {
  height: 40px;
  width: 40px;
  border-radius: 50%;

  .card-icon-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin: 0;
  }
}
.card-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;


  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    min-width: 0;

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

:host ::ng-deep .awe-card-body ul li {
  line-height: 1.4;
  word-wrap: break-word;
  overflow:hidden;

}

@media (min-width: 990px) and (max-width: 1440px) {
  .column-container {
    &.first-column,
    &.third-column {
     min-height: 100%
    }
  }

  .column-container.second-column {
    flex-direction: column; // Stack cards vertically
    gap: 0.5rem;
    height: 100%;
    overflow: hidden; // Allow scrolling if content overflows

    > awe-card {
      width: 100%;
      // max-height: 32%;
      min-height: 300px; // Ensure consistent height
      max-height: 300px; // Prevent cards from growing too large
      flex: none;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Medium screens (md) - 768px to 991.98px
@media (max-width: 991.98px) and (min-width: 768px) {
  .canvas-row {
    flex-direction: coloum;
  }

  .column-container {
    &.first-column,
    &.third-column {
      flex-direction: row;
      gap: 0.5rem;
    }
    &.second-column { // Cards stack vertically
      flex-direction: column;
      max-height: 32%;
      gap: 0; //use margin on cards instead of gap for vertical stacking

      > awe-card {
        max-width: 100%;
        margin-bottom: 0.5rem;
        flex-grow: 0; // Don't grow to fill vertical space
        flex-shrink: 0;
        flex-basis: auto;
        overflow: hidden; // Prevent overflow
        height: 100%; // Ensure cards take full height of the column
        min-height: 300px; // Ensure consistent height
        max-height: 300px; // Prevent cards from growing too large

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // Adjust font size in projected header for MD screens
  .custom-card-header h6 {
    font-size: 0.8rem;
  }

}

// Small screens (sm) - max-width 767.98px
@media (max-width: 767.98px) {
  .column-container {
    flex-direction: column; // Stack all cards vertically
    gap: 0; // Use margin on cards instead
    height: 100%;

    &.first-column,
    &.second-column,
    &.third-column {
      flex-direction: column;
      gap: 0; // Use margin on cards

      > awe-card {
        width: 100%; // Same width for all cards
        height: 200px; // Fixed height for all cards
        min-height: 300px; // Ensure consistent height
        max-height: 200px; // Prevent cards from growing
        margin-bottom: 0.5rem;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Extra small screens (xs) - max-width 575.98px
@media (max-width: 575.98px) {
  .column-container {
    &.first-column,
    &.second-column,
    &.third-column {
      > awe-card {
        width: 100%; // Same width for all cards
        height: 100%; // Smaller fixed height for extra small screens
        min-height: 300px; // Ensure consistent height
        max-height: 300px; // Prevent cards from growing
        margin-bottom: 0.5rem;
      }
    }
  }

  // Adjust styles for projected content on XS screens
  .custom-card-header h6 {
    font-size: 0.7rem;
  }

  .card-icon { // Projected icon
    width: 32px;
    height: 32px;

    .card-icon-img {
      width: 20px;
      height: 20px;
    }
  }
}