<div class="container-fluid p-3" (click)="closeAllDropdowns()">
  <div class="row g-3"> <!-- g-3 provides gap between columns -->
    <div *ngFor="let section of sections" class="col-12 col-sm-6 col-lg-3 d-flex flex-column">
      <!-- Section Main Header (Mo, S, Co, W) -->
      <div class="section-header text-center text-white rounded-top" [id]="section.id + '-header'">
        <div class="section-title typography">
          {{ section.title }}
        </div>
      </div>

      <!-- Section Subtitle Header (MUST HAVE, SHOULD HAVE, etc.) -->
      <div class="section-subtitle-header text-center mb-3" [id]="section.id + '-subtitle-header'">
        <div class="section-subtitle-text">
          {{ section.subtitle.toLocaleUpperCase() }}
        </div>
      </div>

      <!-- Drop Zone for Features -->
      <div cdkDropList [id]="section.id" [cdkDropListData]="section.features" [cdkDropListConnectedTo]="getSectionIds()"
        (cdkDropListDropped)="onDrop($event)"
        class="feature-list-dropzone border border-top-0 p-2 d-flex flex-column gap-3 flex-grow-1">
        <!-- Feature Cards using awe-card -->
        <awe-card
          *ngFor="let feature of section.features"
          [showHeader]="true"
          [showBody]="true"
          [applyHeaderPadding]="true"
          [applyBodyPadding]="true"
          cardClass="feature-item-card"
          cdkDrag
          (cdkDragStarted)="$event.source.element.nativeElement.style.cursor = 'grabbing'"
          (cdkDragEnded)="$event.source.element.nativeElement.style.cursor = 'grab'">

          <!-- Projected Header for awe-card -->
          <div awe-card-header-content class="d-flex justify-content-between align-items-center">
            <awe-heading variant="s2" type="bold" class="feature-title mb-0 flex-grow-1 pe-2">{{ feature.title }}</awe-heading>
            <div class="dropdown-menu-container position-relative"> <!-- Added container for better click handling -->
              <button class="btn btn-link p-0 text-dark action-button" type="button" (click)="toggleDropdown(feature.id, $event)">
                <img [src]="threeDotsIcon" alt="menu" class="action-icon">
              </button>
              <div class="dropdown-menu dropdown-menu-end position-absolute" [class.show]="isDropdownOpen(feature.id)">
                <button class="dropdown-item" type="button" (click)="openEditDialog(feature)">Edit</button>
                <button class="dropdown-item text-danger" type="button" (click)="handleDeleteFeatureFromCard(section.id, feature.id)">
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Projected Body for awe-card (Default Slot) -->
          <p class="feature-description text-muted small mb-3 flex-grow-1">{{ feature.description }}</p>
          <div class="d-flex flex-wrap gap-1">
            <span *ngFor="let tag of feature.tags" class="feature-tag badge rounded-pill px-2 py-1">
              {{ tag }}
            </span>
          </div>
        </awe-card>

        <!-- Empty State for Drop Zone -->
        <div *ngIf="section.features.length === 0" class="text-center text-muted fst-italic py-4">
          Drag and drop features here
        </div>
      </div>

      <!-- Add More Button -->
      <div class="add-more-section text-center">
        <button class="add-more-btn w-100" (click)="addNewFeature(section.id)">
          Add more <span class="plus-icon">+</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Edit Dialog Component (remains the same) -->
<app-edit-dialog
  [isVisible]="isDialogVisible"
  [config]="dialogConfig"
  [dropdownItems]="dropdownItems"
  (closeDialog)="closeDialog()"
  (dropdownAction)="onDialogDropdownAction($event)">
  <div *ngIf="currentEditingFeature" class="dialog-content">
    <div class="mb-3">
      <label for="featureTitle" class="form-label fw-semibold">Title :</label>
      <input
        type="text"
        id="featureTitle"
        class="form-control"
        [(ngModel)]="currentEditingFeature.title"
        placeholder="Enter feature title">
    </div>
    <div class="mb-3">
      <label for="featureDescription" class="form-label fw-semibold">Description :</label>
      <textarea
        id="featureDescription"
        class="form-control"
        rows="4"
        [(ngModel)]="currentEditingFeature.description"
        placeholder="Enter feature description"></textarea>
    </div>
     <!-- Tags editing - simple example, could be more complex -->
    <div class="mb-3">
      <label for="featureTags" class="form-label fw-semibold">Tags (comma-separated):</label>
      <input
        type="text"
        id="featureTags"
        class="form-control"
        [value]="currentEditingFeature.tags.join(', ')"
        (input)="updateFeatureTags($event)"
        placeholder="e.g., Convenience, UX, Tech">
    </div>
    <div class="mb-3">
      <label class="form-label fw-semibold">Regenerate</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          placeholder="Type your prompt here...">
        <button class="btn btn-outline-secondary" type="button">
          <i class="fas fa-paper-plane"></i> <!-- Make sure Font Awesome is linked -->
        </button>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button type="button" class="btn btn-primary px-4" (click)="saveEditedFeature()">Update</button>
    </div>
  </div>
</app-edit-dialog>