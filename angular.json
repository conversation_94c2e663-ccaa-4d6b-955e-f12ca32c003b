{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"elderWand": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/elder-wand", "sourceRoot": "projects/elder-wand/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/elder-wand", "index": "projects/elder-wand/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/elder-wand/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/elder-wand/public", "output": "."}, {"glob": "staticwebapp.config.json", "input": ".", "output": "/"}], "styles": ["projects/elder-wand/src/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss"], "scripts": [], "main": "projects/elder-wand/src/main.ts", "extraWebpackConfig": "projects/elder-wand/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "1MB", "maximumError": "50kb"}], "outputHashing": "all", "extraWebpackConfig": "projects/elder-wand/webpack.prod.config.js", "fileReplacements": [{"replace": "projects/elder-wand/src/environments/environment.ts", "with": "projects/elder-wand/src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "elderWand:build:production", "extraWebpackConfig": "projects/elder-wand/webpack.prod.config.js"}, "development": {"buildTarget": "elderWand:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4200, "publicHost": "http://localhost:4200", "extraWebpackConfig": "projects/elder-wand/webpack.config.js"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/elder-wand/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/elder-wand/public"}], "styles": ["projects/elder-wand/src/styles.scss"], "scripts": []}}}}, "experienceStudio": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/experience-studio", "sourceRoot": "projects/experience-studio/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/experience-studio", "index": "projects/experience-studio/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/experience-studio/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/experience-studio/public", "output": "."}, {"glob": "**/*", "input": "projects/experience-studio/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "node_modules/@awe/play-comp-library/src/lib/assets/icons", "output": "assets/icons"}, {"glob": "staticwebapp.config.json", "input": ".", "output": "/"}], "styles": ["projects/experience-studio/src/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss"], "scripts": [], "main": "projects/experience-studio/src/main.ts", "extraWebpackConfig": "projects/experience-studio/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "1MB", "maximumError": "70kb"}], "outputHashing": "all", "extraWebpackConfig": "projects/experience-studio/webpack.prod.config.js", "fileReplacements": [{"replace": "projects/experience-studio/src/environments/environment.ts", "with": "projects/experience-studio/src/environments/environment.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "experienceStudio:build:production", "extraWebpackConfig": "projects/experience-studio/webpack.prod.config.js"}, "development": {"buildTarget": "experienceStudio:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4201, "publicHost": "http://localhost:4201", "extraWebpackConfig": "projects/experience-studio/webpack.config.js"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/experience-studio/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/experience-studio/src/assets", "output": "/assets/"}, {"glob": "**/*", "input": "projects/play-comp-library/src/lib/assets", "output": "/assets/"}], "styles": ["projects/experience-studio/src/styles.scss"], "scripts": []}}}}, "product-studio": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/product-studio", "sourceRoot": "projects/product-studio/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/product-studio", "index": "projects/product-studio/src/index.html", "main": "projects/product-studio/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/product-studio/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/product-studio/public", "output": "."}, {"glob": "staticwebapp.config.json", "input": ".", "output": "/"}], "styles": ["projects/product-studio/src/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss"], "scripts": [], "extraWebpackConfig": "projects/product-studio/webpack.config.js", "commonChunk": false}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "50kb"}], "outputHashing": "all", "extraWebpackConfig": "projects/product-studio/webpack.prod.config.js"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "options": {"port": 4202, "publicHost": "http://localhost:4202", "buildTarget": "product-studio:build:development", "extraWebpackConfig": "projects/product-studio/webpack.config.js"}, "configurations": {"production": {"buildTarget": "product-studio:build:production", "extraWebpackConfig": "projects/product-studio/webpack.prod.config.js"}, "development": {"buildTarget": "product-studio:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "product-studio:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/product-studio/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/product-studio/src", "output": "/"}], "styles": ["projects/product-studio/src/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/styles.scss", "node_modules/@awe/play-comp-library/src/lib/styles/tokens.scss"], "scripts": []}}}}, "console": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/console", "sourceRoot": "projects/console/src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist/console", "index": "projects/console/src/index.html", "polyfills": ["zone.js"], "tsConfig": "projects/console/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/console/public", "output": "."}, {"glob": "**/*", "input": "projects/console/src/assets", "output": "/assets/"}, {"glob": "staticwebapp.config.json", "input": ".", "output": "/"}], "styles": ["projects/console/src/styles.scss"], "scripts": [], "main": "projects/console/src/main.ts", "extraWebpackConfig": "projects/console/webpack.config.js", "commonChunk": false, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "1MB"}]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "50kb"}], "outputHashing": "all", "extraWebpackConfig": "projects/console/webpack.prod.config.js", "fileReplacements": [{"replace": "projects/console/src/environments/environment.ts", "with": "projects/console/src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": true}, "sourceMap": false, "namedChunks": false, "vendorChunk": false, "buildOptimizer": true, "aot": true}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "console:build:production", "extraWebpackConfig": "projects/console/webpack.prod.config.js"}, "development": {"buildTarget": "console:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4203, "publicHost": "http://localhost:4203", "extraWebpackConfig": "projects/console/webpack.config.js"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/console/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/console/public", "output": "."}, {"glob": "**/*", "input": "projects/console/src/assets", "output": "/assets/"}], "styles": ["projects/console/src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "a994879d-2058-4e78-9973-abebd4e404dc"}}