// Main row container with equal height columns
.canvas-row {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch; // Ensures columns stretch to the height of the tallest

  // Ensure all bootstrap columns within canvas-row are flex containers
  // to allow awe-card to fill their height.
  > [class*="col-"] {
    display: flex;
    flex-direction: column; // Stack content vertically within the column div
  }
}

// Column containers for holding awe-card instances
.column-container {
  display: flex;
  height: 100%;
  width: 100%;

  &.first-column,
  &.third-column {
    flex-direction: column; // Default: cards stack vertically
    gap: 0.5rem;
    > awe-card {
      flex: 1;
    }
  }

  &.second-column {
    flex-direction: row; // Default: cards are horizontal (applies to > 991.98px unless overridden)
    gap: 0.5rem;
    > awe-card {
      flex: 1; // Cards share horizontal space
    }
  }
}

.card-icon { // Icon container within the projected header
  width: 40px;
  height: 40px;
  border-radius: 50%;
  // background-color is set via [style.background-color] in HTML
  // flex-shrink: 0 is on the div in HTML

  .card-icon-img { // The actual image
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin: 0; // fs-5 from bootstrap might affect this, ensure it's reset if needed
  }
}

.card-actions { // Container for action buttons
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // gap: 8px; // Optional gap between buttons

  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0; // Remove default button padding
    min-width: 0; // Override Bootstrap default min-width for buttons

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

:host ::ng-deep .awe-card-body ul li { // Target li inside awe-card's body
  line-height: 1.4;
  word-wrap: break-word;
  // mb-2 and d-flex align-items-start are Bootstrap classes on the li
}

// Rule for 990px to 1440px (Second column cards stack)
@media (min-width: 990px) and (max-width: 1440px) {
  .column-container.second-column {
    flex-direction: coloum; // This was making them stack
    gap: 0.5rem;

    > awe-card {
      flex: 1; // Ensure cards share space horizontally
      // Reset any min-height or width: 100% that might have been set for stacked views
      min-height: 0; // Or whatever appropriate min-height for horizontal cards
      width: auto; // Let flexbox determine width based on flex: 1
      margin-bottom: 0; // Remove bottom margin if it was for stacked cards
    }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

// Medium screens (md) - 768px to 991.98px
@media (max-width: 991.98px) and (min-width: 768px) {
  .canvas-row {
    flex-direction: row; // Reset min-height if set for larger screens
  }

  .column-container {
    &.first-column,
    &.third-column {
      flex-direction: row; // Cards side-by-side
      gap: 0.5rem;
    }

    &.second-column { // Cards stack vertically
      flex-direction: column;
      gap: 0; // Use margin on cards instead of gap for vertical stacking

      > awe-card {
        width: 100%;
        min-height: 150px;
        margin-bottom: 0.5rem;
        flex-grow: 0; // Don't grow to fill vertical space
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // Adjust font size in projected header for MD screens
  .custom-card-header h6 {
    font-size: 0.8rem;
  }
 
}

// Small screens (sm) - max-width 767.98px
@media (max-width: 767.98px) {
  .canvas-row {
    min-height: auto;
  }

  .column-container {
  
    &.first-column,
    &.second-column,
    &.third-column {
      flex-direction: column;
      gap: 0; // Use margin on cards

      > awe-card {
        width: 100%;
        min-height: 120px;
        margin-bottom: 0.5rem;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Extra small screens (xs) - max-width 575.98px (Bootstrap 5's sm breakpoint is 576px)
// If using Bootstrap 4, xs is <576px.
@media (max-width: 575.98px) {
  .column-container > awe-card { // Target all awe-cards in column-containers
    min-height: 100px; // Further reduce min-height for very small screens
  }

  // Adjust styles for projected content on XS screens
  .custom-card-header h6 {
    font-size: 0.7rem;
  }
  .card-icon { // Projected icon
    width: 32px;
    height: 32px;

    .card-icon-img {
      width: 20px;
      height: 20px;
    }
  }
}