// Import variables - adjust path as needed 
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@700;900&family=Mulish:wght@500&display=swap');

// General container styling (from your image, it's white/light gray)
.container-fluid {
  background-color: #f8f9fa; // Light background for the whole page
}

// Section Headers
.section-header {
  height: 100px; // As per image
  display: flex;
  align-items: center;
  justify-content: center;
  color: $section-header-main-title-color;
  // rounded-top is a Bootstrap class

  .section-title.typography {
    font-family: 'Inter', sans-serif;
    font-size: 96px;
    font-weight: 900; // For the thick letters like MO, S
    line-height: 1; // Adjust for better vertical centering
    text-transform: uppercase;
    letter-spacing: -2px; // Tighten spacing
  }
}

.section-subtitle-header {
  height: 48px; // As per image
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $section-header-subtitle-bg; // White bar

  .section-subtitle-text {
    font-family: 'Inter', sans-serif;
    font-size: 32px; // Smaller than main title
    font-weight: 700;
    line-height: 1;
    text-transform: uppercase;
    color: $section-header-subtitle-text-color;
  }
}

// Section-specific header background colors
#must-have-header { background-color: $must-have-header-bg !important; }
#should-have-header { background-color: $should-have-header-bg !important; }
#could-have-header { background-color: $could-have-header-bg !important; }
#wont-have-header { background-color: $wont-have-header-bg !important; }


// Dropzone for features
.feature-list-dropzone {
  background-color: $gray-100; // Light gray background for the droppable area
  border-color: $default-border-color !important; // Ensure border is visible
  min-height: 200px; // Minimum height for empty dropzones
  // gap-3 from Bootstrap gives space between cards
  // flex-grow-1 ensures it takes available vertical space in the column
}

// Styling for awe-card instances in this feature list context
// We use cardClass="feature-item-card" on awe-card
app-feature-list awe-card.feature-item-card {
  // Override awe-card CSS variables if needed for this specific context
  --awe-card-background: $gray-soft-bg; // #FBFBFB from your original SCSS
  --awe-card-border: 1px solid $gray-200; // Lighter border than awe-card default
  --awe-card-box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1); // Softer shadow
  --awe-card-border-radius: 0.5rem; // 8px, Bootstrap's rounded is 0.375rem
  --awe-card-padding-base: 1rem; // p-3 in Bootstrap is 1rem

  min-height: 185px; // From your original .feature-card
  display: flex; // Ensure it behaves as a flex container if its content needs to grow
  flex-direction: column; // Stack header/body/footer

  &:hover {
    --awe-card-box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Custom hover shadow
    transform: translateY(-2px); // Your hover effect
  }

  // Styles for content projected into this specific awe-card
  .feature-title {
    font-family: 'Mulish', sans-serif; // Example, adjust as needed
    font-weight: 600; // fw-semibold equivalent
    color: $feature-title-color;
    font-size: 1rem; // Adjust as needed, awe-heading might have its own
  }

  .feature-description {
    font-family: 'Mulish', sans-serif;
    color: $feature-description-color;
    font-size: 0.875rem; // small text
    line-height: 1.5;
  }

  .feature-tag {
    font-family: 'Mulish', sans-serif;
    background-color: $tag-bg;
    color: $tag-text-color;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .action-button {
    img.action-icon {
      width: 20px; // Adjust icon size if needed
      height: 20px;
    }
  }
}


// Dropdown Menu (projected into awe-card header)
.dropdown-menu-container { // Wrapper for button and menu
  .dropdown-menu {
    // Using Bootstrap classes: dropdown-menu, dropdown-menu-end, position-absolute
    // Customizations:
    min-width: 120px;
    border-radius: 0.5rem; // Rounded corners
    box-shadow: 0 0.5rem 1rem rgba($black, 0.15); // Standard shadow
    padding: 0.5rem 0; // Vertical padding
    margin-top: 0.25rem; // Small space from button
    z-index: 1050; // Ensure it's above other content

    .dropdown-item {
      padding: 0.5rem 1rem; // Consistent padding
      font-family: 'Mulish', sans-serif;
      font-size: 0.875rem;
      border-bottom: 1px solid $gray-100; // Light separator

      &:last-child {
        border-bottom: none;
      }

      &:hover, &:focus {
        background-color: $gray-100;
      }

      &.text-danger { // Bootstrap class
        color: $dropdown-danger-text-color !important;
        &:hover, &:focus {
          background-color: $dropdown-danger-hover-bg;
          color: $dropdown-danger-hover-color !important;
        }
      }
    }
  }
}


// Add More Button Section
.add-more-section {
  background-color: $add-more-button-bg;
  // padding will be on the button itself if it's w-100

  .add-more-btn {
    background-color: transparent; // Section provides background
    border: none;
    color: $add-more-button-text-color;
    font-family: 'Mulish', sans-serif;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem; // Make it feel like a button
    height: 48px; // Match image
    display: flex;
    align-items: center;
    justify-content: center;

    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}

// CDK Drag and Drop animations (from your SCSS, looks good)
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem; // Match card's border-radius
  box-shadow: 0 8px 25px rgba($black, 0.15), 0 4px 10px rgba($black, 0.1);
  transform: rotate(2deg);
  // Transition applied by CDK
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba($gray-500, 0.1);
  border: 2px dashed $gray-500;
  border-radius: 0.5rem; // Match card
  min-height: 185px; // Match card min-height
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.feature-list-dropzone.cdk-drop-list-dragging awe-card.feature-item-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

// Ensure grabbing cursor on the awe-card when dragging
:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}