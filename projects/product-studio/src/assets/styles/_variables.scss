:root {
  // Light Theme Variables
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  // background-image: url('../svgs/main-bg-light.svg');
  --background-color: #fff;
  --body-text-color: #000000;
  --main-title: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --main-subtitle: #666d99;

  --card-border-color: #292c3d;
  --card-hover-bg: #f5f5f5;
  --selected-card-border: #3f51b5;
  --selected-card-bg: #e8eaf6;
  --button-bg: #cccccc;
  --button-hover-bg: #b3b3b3;
  --summary-bg: #f9f9f9;
  --summary-border: #dddddd;
  --option-card-background-color: #fafafa;

  --header-icon: #000000;
  --header-icon-border: #c2c5d6;
  --button-bg-color: #ffffff;
  --button-text-color: #292c3d;
  --button-border-color: #fff;

  //main-component hero-section and prompt-bar
  --hero-section-color: #14161f;
  --hero-section-heading-color: #dadce7;
  --prompt-bar-background-color: #000;
  --prompt-bar-border-color: #f96cab;
  --prompt-bar-hover-color: #6566cd;
  --prompt-bar-suggestion-button-bg: linear-gradient(
    102deg,
    rgba(240, 240, 245, 0.72) 2.05%,
    rgba(240, 240, 245, 0.72) 100%
  );

  // feature-card-main-page
  --feature-card-border-back: #e30a6d;
  --feature-card-btn-text-color: #ffffff;
  --feature-card-bg: rgb(250, 247, 247);
  --feature-card-border-front: 1.5px solid #fff;
  --loading-text-color: #14161f;
  --feature-card-text-color: #14161f;
  --feature-card-color: #070707;
  --feature-card-btn: linear-gradient(
    90deg,
    var(--Blue-B-500, #6566cd) 0%,
    var(--Pink-P-500, #e30a6d) 100%
  );

  // form-factor-component light theme
  --form-factor-btn-border: #29292c;
  --form-factor-btn-bg: none;
  --form-factor-card-hover: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --form-factor-btn-text-color: #29292c;
  --right-panel-header-bg: #f0f0f580;

  // Code Viewer Variables - Light Theme
  --code-viewer-bg: rgba(240, 240, 245, 0.5);
  --code-viewer-text: #000000;
  --code-viewer-border: #e0e0e0;
  --code-viewer-file-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-folder-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-search-bg: transparent;
  --code-viewer-search-border: #e0e0e0;
  --code-viewer-search-focus: #333333;
  --code-viewer-search-placeholder: #666666;
  --code-viewer-arrow: #000000;
  --code-viewer-tab-bg: #f5f5f5;
  --code-viewer-tab-hover: rgba(0, 0, 0, 0.05);
  --code-viewer-tab-text: #000000;
  --code-viewer-monaco-bg: transparent;
  --code-viewer-tree-text: #000000;
  --toggle-button-background-color: #ffff;
  --toggle-button-icon-background-color: #ffff;
  --toggle-switch-checked-bg-color: #851585;
  --preview-page-bg-color: #ffff;
  --pill-text-color: #000;
  --code-viewer-header-tab: #858AAD;
  --icon-disabled-color: #8a8d9a; /* Darker gray for better visibility in light mode */
  --icon-enabled-color: #52577a;
  --icon-disabled-opacity: 0.4; /* Higher opacity for better visibility */

  // Logs Container Variables - Light Theme
  --logs-container-bg: #ffffff;
  --logs-container-border: #e0e0e0;
  --logs-header-bg: #f8f9fa;
  --logs-header-border: #e0e0e0;
  --logs-content-bg: #f5f5f5;
  --logs-text-color: #333333;
  --logs-filter-hover-bg: rgba(0, 0, 0, 0.05);
  --logs-filter-active-bg: rgba(0, 0, 0, 0.1);
  --logs-info-message-bg: rgba(2, 136, 209, 0.1);
  --logs-info-message-color: #0288d1;
  --logs-info-color: #0288d1;
  --logs-debug-color: #7b1fa2;
  --logs-warning-color: #ffa000;
  --logs-error-color: #d32f2f;
  --logs-streaming-bg: rgba(2, 136, 209, 0.1);
  --logs-streaming-color: #0288d1;

  // split screen Resizer
  --awe-split-screen-resizer: #fff;
  --awe-split-screen-resizer-hover: #808080;

  // Split Screen Header
  --awe-split-screen-header-bg-color: rgba(240, 240, 245, 0.5);
  --awe-split-border-color: #dadce7;
  --chat-window-card-bg: #f8f8ff;
  --chat-window-text-color: #14161f;
  --chat-window-icon-color: #666d99;

  /* Scrollbar Variables - Light Theme */
  --scrollbar-thumb: rgba(101, 102, 205, 0.3);
  --scrollbar-thumb-hover: rgba(101, 102, 205, 0.5);
  --chat-window-card-bg-color: #f8f8ff;
  --chat-window-card-border: transparent;
  --modal-text-color:#14161f;



// --- LIGHT THEME PALETTE ---
$lt-white: #fff;
$lt-black: #000;
$lt-gray-900: #212529; // Bootstrap dark text
$lt-gray-800: #343a40;
$lt-gray-700: #495057; // Bootstrap secondary text
$lt-gray-600: #6c757d; // Bootstrap text-muted
$lt-gray-500: #808080; // Your original $gray-500
$lt-gray-400: #a0a0a0; // Your original $gray-400
$lt-gray-300: #cccccc; // Your original $gray-300, or Bootstrap $gray-300: #dee2e6;
$lt-gray-200: #e0e0e0; // Your original $gray-200, or Bootstrap $gray-200: #e9ecef;
$lt-gray-100: #f0f0f0; // Your original $gray-100, or Bootstrap $gray-100: #f8f9fa;
$lt-gray-soft-bg: #FBFBFB; // Your card background

$lt-must-have-header-bg: #808080;
$lt-should-have-header-bg: #707070;
$lt-could-have-header-bg: #606060;
$lt-wont-have-header-bg: #a0a0a0;

$lt-section-header-main-title-color: $lt-white;
$lt-section-header-subtitle-bg: $lt-white;
$lt-section-header-subtitle-text-color: $lt-gray-500; // #808080

$lt-feature-title-color: $lt-gray-800;
$lt-feature-description-color: $lt-gray-600;

$lt-tag-bg: $lt-gray-100; // Or a Bootstrap subtle color like #e2e3e5 (bg-secondary-subtle)
$lt-tag-text-color: $lt-gray-800;

$lt-add-more-button-bg: #989898;
$lt-add-more-button-text-color: $lt-white;

$lt-dropdown-menu-bg: $lt-white;
$lt-dropdown-item-text-color: $lt-gray-900;
$lt-dropdown-item-hover-bg: $lt-gray-100;
$lt-dropdown-item-border-color: $lt-gray-200; // Light separator
$lt-dropdown-danger-text-color: #dc3545;
$lt-dropdown-danger-hover-bg: #f5c2c7;
$lt-dropdown-danger-hover-color: #842029;

$lt-default-border-color: $lt-gray-300; // #cccccc or #dee2e6 (Bootstrap border)
$lt-app-background: $lt-gray-100; // Overall page background


}

.dark-theme {
  // Dark Theme Variables
  overflow: auto;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-image: url('../svgs/main-bg.svg');
  // --background-gradient-color:linear-gradient(to bottom right, #a855f7, #3b82f6, #ec4899, #f97316);
  --background-color: #121212;
  --body-text-color: #e0e0e0;
  --card-border-color: #424242;
  --card-hover-bg: #2a2a2a;
  --selected-card-border: #7986cb;
  --selected-card-bg: #303f9f;
  --button-bg: #424242;
  --button-hover-bg: #525252;
  --summary-bg: #2a2a2a;
  --summary-border: #424242;
  --hero-section-color: #ffffff;
  --hero-section-heading-color: #33364d;
  --option-card-background-color: none;
  --header-icon: #ffffff;
  --header-icon-border: #ffffff;
  --button-bg-color: #e04574;
  --button-text-color: #ededf3;
  --button-border-color: #474c6b;
  --prompt-bar-background-color: #8b8dda;
  --prompt-bar-border-color: #8b8dda;
  --prompt-bar-hover-color: #f96cab;
  --feature-card-color: #ffffff;
  --prompt-bar-suggestion-button-bg: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.36) 2.05%,
    rgba(20, 27, 31, 0.36) 100%
  );

  --main-title: #fff;
  --main-subtitle: #fff;

  // feature-card-main-page
  --feature-card-btn-text-color: #ffffff;
  --feature-card-border-front: #fff;
  --feature-card-text-color: #fff;
  --feature-card-border-back: #e30a6d;
  --feature-card-bg: linear-gradient(
    102deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
  --feature-card-btn: linear-gradient(
    90deg,
    var(--Blue-B-600, rgba(66, 68, 194, 0.75)) 0%,
    var(--Pink-P-600, rgba(193, 8, 92, 0.75)) 100%
  );

  // form-factor-component dark theme
  --form-factor-btn-border: #fff;
  --form-factor-btn-bg: none;
  --form-factor-card-hover: linear-gradient(90deg, #6566cd 24%, #f63b8f 68%);
  --form-factor-btn-text-color: #fff;
  --right-panel-header-bg: #14161f;

  // Code Viewer Variables - Dark Theme
  --code-viewer-bg: rgba(20, 27, 31, 0.24);
  --code-viewer-text: #ffffff;
  --code-viewer-border: #292c3d;
  --code-viewer-file-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-folder-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-search-bg: transparent;
  --code-viewer-search-border: #292c3d;
  --code-viewer-search-focus: #4b4e61;
  --code-viewer-search-placeholder: #666;
  --code-viewer-arrow: #fff;
  --code-viewer-tab-bg: rgba(255, 255, 255, 0.05);
  --code-viewer-tab-hover: rgba(255, 255, 255, 0.1);
  --code-viewer-tab-text: #fff;
  --code-viewer-monaco-bg: transparent;
  --code-viewer-tree-text: #ffffff;
  --toggle-button-background-color: #1a1c25;
  --toggle-button-icon-background-color: #14161f;
  --toggle-switch-checked-bg-color: #6366f1;
  --preview-page-bg-color: #1a1c25;
  --pill-text-color: #fff;
  --loading-text-color: #ffffff;
  --code-viewer-header-tab:#292C3D
  --icon-disabled-color: #c2c5d6; /* Light gray for dark mode */
  --icon-enabled-color: #ffffff; /* Bright white for enabled state in dark mode */
  --icon-disabled-opacity: 0.4; /* Opacity for disabled state */

  // Logs Container Variables - Dark Theme
  --logs-container-bg: #14161f;
  --logs-container-border: #292c3d;
  --logs-header-bg: #1a1c25;
  --logs-header-border: #292c3d;
  --logs-content-bg: #14161f;
  --logs-text-color: #ffffff;
  --logs-filter-hover-bg: rgba(255, 255, 255, 0.1);
  --logs-filter-active-bg: rgba(255, 255, 255, 0.15);
  --logs-info-message-bg: rgba(2, 136, 209, 0.2);
  --logs-info-message-color: #64b5f6;
  --logs-info-color: #64b5f6;
  --logs-debug-color: #ce93d8;
  --logs-warning-color: #ffcc80;
  --logs-error-color: #ef9a9a;
  --logs-streaming-bg: rgba(2, 136, 209, 0.2);
  --logs-streaming-color: #64b5f6;

  // Split screen resizer
  --awe-split-screen-resizer: none;
  --awe-split-screen-resizer-hover: #808080;

  // Split Screen Header

  --awe-split-screen-header-bg-color: none;
  --awe-split-border-color: #292c3d;
  --chat-window-card-bg: #33364d;
  --chat-window-text-color: #ffffff;
  --chat-window-icon-color: #ffffff;

  /* Scrollbar Variables - Dark Theme */
  --scrollbar-thumb: rgba(246, 59, 143, 0.4);
  --scrollbar-thumb-hover: rgba(246, 59, 143, 0.6);
  --chat-window-card-bg-color: linear-gradient(
    102.14deg,
    rgba(20, 27, 31, 0.24) 2.05%,
    rgba(20, 27, 31, 0.24) 100%
  );
  --chat-window-card-border: rgba(255, 255, 255, 1);
  --modal-text-color:#ffffff;


// --- DARK THEME PALETTE ---
// Choose colors that work well for a dark UI.
// These are examples and should be refined based on your design.
$dt-black: #000;
$dt-gray-base: #121212; // Common main dark background
$dt-gray-surface: #1e1e1e; // Slightly lighter for surfaces like cards
$dt-gray-surface-2: #2a2a2a; // Even lighter for nested surfaces or hover
$dt-gray-border: #3a3a3a; // Darker borders that are still visible
$dt-gray-text-primary: #e0e0e0; // Primary text (high emphasis)
$dt-gray-text-secondary: #a0a0a0; // Secondary text (medium emphasis)
$dt-gray-text-disabled: #757575; // Disabled text (low emphasis)
$dt-white-contrast: #fff; // For high contrast elements if needed

// Section headers might stay the same if they are meant to be distinct visual blocks,
// or they can be adapted. Let's assume they stay visually similar for now,
// but their text contrast might need to be considered.
$dt-must-have-header-bg: #808080;
$dt-should-have-header-bg: #707070;
$dt-could-have-header-bg: #606060;
$dt-wont-have-header-bg: #a0a0a0;

$dt-section-header-main-title-color: $dt-white-contrast; // Still white on dark gray
$dt-section-header-subtitle-bg: $dt-gray-surface; // Dark background for the subtitle bar
$dt-section-header-subtitle-text-color: $dt-gray-text-secondary; // Lighter gray text

$dt-feature-card-bg: $dt-gray-surface;
$dt-feature-card-border: $dt-gray-border;
// Shadows are often more subtle or emulated with lighter surfaces in dark mode.
// $dt-feature-card-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.5); // Example dark shadow

$dt-feature-title-color: $dt-gray-text-primary;
$dt-feature-description-color: $dt-gray-text-secondary;

$dt-tag-bg: $dt-gray-surface-2; // Slightly different from card bg for distinction
$dt-tag-text-color: $dt-gray-text-primary;

$dt-add-more-button-bg: #787878; // Slightly darker or different shade for dark mode
$dt-add-more-button-text-color: $dt-white-contrast;

$dt-dropdown-menu-bg: $dt-gray-surface-2;
$dt-dropdown-item-text-color: $dt-gray-text-primary;
$dt-dropdown-item-hover-bg: $dt-gray-border; // Or a slightly lighter surface
$dt-dropdown-item-border-color: $dt-gray-border;
$dt-dropdown-danger-text-color: #ff7f7f; // Lighter red for dark mode
$dt-dropdown-danger-hover-bg: #5c1f1f; // Darker red background for hover
$dt-dropdown-danger-hover-color: #ffabab;

$dt-default-border-color: $dt-gray-border;
$dt-app-background: $dt-gray-base;

// --- SEMANTIC COLOR VARIABLES ---
// These variables will be used throughout your SCSS files.
// Their values are determined by the $current-theme.

// Base Page
$app-background-color: if($current-theme == 'light', $lt-app-background, $dt-app-background);
$text-color-primary: if($current-theme == 'light', $lt-gray-900, $dt-gray-text-primary);
$text-color-secondary: if($current-theme == 'light', $lt-gray-700, $dt-gray-text-secondary);
$text-color-muted: if($current-theme == 'light', $lt-gray-600, $dt-gray-text-disabled);
$border-color-default: if($current-theme == 'light', $lt-default-border-color, $dt-default-border-color);

// Section Headers
// These are section-specific, so they are defined directly rather than through a single semantic var,
// but they can still be theme-dependent.
$header-bg-must-have: if($current-theme == 'light', $lt-must-have-header-bg, $dt-must-have-header-bg);
$header-bg-should-have: if($current-theme == 'light', $lt-should-have-header-bg, $dt-should-have-header-bg);
$header-bg-could-have: if($current-theme == 'light', $lt-could-have-header-bg, $dt-could-have-header-bg);
$header-bg-wont-have: if($current-theme == 'light', $lt-wont-have-header-bg, $dt-wont-have-header-bg);

$section-main-title-text-color: if($current-theme == 'light', $lt-section-header-main-title-color, $dt-section-header-main-title-color);
$section-subtitle-background-color: if($current-theme == 'light', $lt-section-header-subtitle-bg, $dt-section-header-subtitle-bg);
$section-subtitle-text-color: if($current-theme == 'light', $lt-section-header-subtitle-text-color, $dt-section-header-subtitle-text-color);

// Feature Card (these would be used to set CSS Custom Properties for awe-card or style projected content)
$feature-card-background: if($current-theme == 'light', $lt-gray-soft-bg, $dt-feature-card-bg);
$feature-card-border: if($current-theme == 'light', $lt-default-border-color, $dt-feature-card-border); // Example, or specific var
// $feature-card-shadow: if($current-theme == 'light', /* light shadow value */, /* dark shadow value */); // Define if needed

$feature-content-title-color: if($current-theme == 'light', $lt-feature-title-color, $dt-feature-title-color);
$feature-content-description-color: if($current-theme == 'light', $lt-feature-description-color, $dt-feature-description-color);

// Tags
$feature-tag-background: if($current-theme == 'light', $lt-tag-bg, $dt-tag-bg);
$feature-tag-text-color: if($current-theme == 'light', $lt-tag-text-color, $dt-tag-text-color);

// Add More Button
$add-more-btn-background: if($current-theme == 'light', $lt-add-more-button-bg, $dt-add-more-button-bg);
$add-more-btn-text-color: if($current-theme == 'light', $lt-add-more-button-text-color, $dt-add-more-button-text-color);

// Dropdown Menu
$dropdown-menu-background: if($current-theme == 'light', $lt-dropdown-menu-bg, $dt-dropdown-menu-bg);
$dropdown-item-text: if($current-theme == 'light', $lt-dropdown-item-text-color, $dt-dropdown-item-text-color);
$dropdown-item-hover-background: if($current-theme == 'light', $lt-dropdown-item-hover-bg, $dt-dropdown-item-hover-bg);
$dropdown-item-separator: if($current-theme == 'light', $lt-dropdown-item-border-color, $dt-dropdown-item-border-color);
$dropdown-item-danger-text: if($current-theme == 'light', $lt-dropdown-danger-text-color, $dt-dropdown-danger-text-color);
$dropdown-item-danger-hover-bg: if($current-theme == 'light', $lt-dropdown-danger-hover-bg, $dt-dropdown-danger-hover-bg);
$dropdown-item-danger-hover-text: if($current-theme == 'light', $lt-dropdown-danger-hover-color, $dt-dropdown-danger-hover-color);




}
